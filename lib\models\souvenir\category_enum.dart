/// 奖励分类枚举类
///
/// 用于定义不同的奖励类别
/// 包含[value]和[label]两个属性,[value]是API传递的值,[label]是显示的标签
enum ItemCategory {
  all('all', '全部'),
  postcard('postcard', '明信片'),
  ticket('ticket', '车票');

  const ItemCategory(this.value, this.label);

  final String value; // API传递的值
  final String label; // 显示的标签

  // 从字符串值获取枚举
  static ItemCategory fromString(String value) {
    return ItemCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => ItemCategory.all,
    );
  }
}
