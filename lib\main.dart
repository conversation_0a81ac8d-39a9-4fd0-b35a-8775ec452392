import 'dart:io';

import 'package:chickafocus/pages/main_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  initialize();

  runApp(ProviderScope(child: const ChickApp()));
}

void initialize() async {
  // 沉浸式状态栏
  if (Platform.isAndroid) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
      ),
    );
  }
}

class ChickApp extends StatelessWidget {
  const ChickApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '小鸡旅行',
      theme: ThemeData(colorScheme: ColorScheme.fromSeed(seedColor: Colors.white)),
      home: const ChickMainPage(),
    );
  }
}
