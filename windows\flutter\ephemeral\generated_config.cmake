# Generated code do not commit.
file(TO_CMAKE_PATH "E:\\Develop\\fvmcache\\versions\\stable" FLUTTER_ROOT)
file(TO_CMAKE_PATH "E:\\GitHub\\chickafocus" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=E:\\Develop\\fvmcache\\versions\\stable"
  "PROJECT_DIR=E:\\GitHub\\chickafocus"
  "FLUTTER_ROOT=E:\\Develop\\fvmcache\\versions\\stable"
  "FLUTTER_EPHEMERAL_DIR=E:\\GitHub\\chickafocus\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=E:\\GitHub\\chickafocus"
  "FLUTTER_TARGET=E:\\GitHub\\chickafocus\\lib\\examples\\souvenir_list_demo.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuOA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZWRhZGE3YzU2ZQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZWYwY2QwMDA5MQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=E:\\GitHub\\chickafocus\\.dart_tool\\package_config.json"
)
