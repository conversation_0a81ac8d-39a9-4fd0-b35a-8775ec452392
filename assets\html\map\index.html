<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="initial-scale=1.0, user-scalable=no, width=device-width"
    />
    <title>AMAP</title>
    <style>
      html,
      body,
      #container {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
      }
    </style>
    <script type="text/javascript">
      window._AMapSecurityConfig = {
        securityJsCode: "4ef657a379f13efbbf096baf8b08b3ed",
      };
    </script>
    <script src="https://webapi.amap.com/loader.js"></script>
  </head>
  <body>
    <div id="container"></div>

    <script type="text/javascript">
      var map;
      var mapLoaded = false;

      var markers = [];

      // 异步加载高德地图并初始化
      AMapLoader.load({
        key: "82ea7ca3d47546f079185e7ccdade9ba",
        version: "2.0",
      })
        .then((AMap) => {
          // 浏览器 UA 信息
          var ua = AMap.Browser.ua;
          // 浏览器平台类型
          var plat = AMap.Browser.plat;
          // 浏览器是否支持 Canvas
          var canvas = AMap.Browser.isCanvas;
          // 浏览器是否支持 WebGL
          var isWebGL = AMap.Browser.isWebGL;
          console.log("浏览器 UA 信息：", ua);
          console.log("浏览器平台类型：", plat);
          console.log("浏览器是否支持 Canvas：", canvas);
          console.log("浏览器是否支持 WebGL：", isWebGL);
          // 初始化地图
          map = new AMap.Map("container", {
            zoom: 6,
            scrollWheel: false,
            doubleClickZoom: false,
            dragEnable: false,
            keyboardEnable: false,
            // 移动端操作
            touchZoom: false,
            dragEnable: false,
          });
          console.log("高德地图初始化完成");
          mapLoaded = true;

          // 地图加载完成后，可以通过这个方法通知外部（Flutter）
          if (window.onMapReady) {
            window.onMapReady();
          }
        })
        .catch((e) => {
          console.error("地图加载失败：", e);
        });

      // 外部调用的函数，用于安全地设置地图缩放和中心点
      function setZoomAndCenter(zoom, center, immediately, duration) {
        if (!mapLoaded) {
          console.error("地图尚未初始化，无法调用 setZoomAndCenter");
          return;
        }
        map.setZoomAndCenter(zoom, center, immediately, duration);
      }

      // 外部调用的函数，用于安全地添加圆形标记
      function addCircleMarker(lng, lat, radius) {
        if (!mapLoaded) {
          console.error("地图尚未初始化，无法调用 addCircleMarker");
          return;
        }
        if (!lng || !lat) {
          console.error("经纬度是必需的");
          return;
        }
        var center = new AMap.LngLat(lng, lat);
        var circleMarker = new AMap.CircleMarker({
          center: [lng, lat],
          radius: radius || 16,
          strokeColor: "black",
          strokeWeight: 2,
          strokeOpacity: 0.3,
          fillColor: "rgba(233,233,233,1)",
          fillOpacity: 0.9,
          zIndex: 10,
          cursor: "pointer",
        });
        markers.push(circleMarker);
        map.add(circleMarker);
        console.log("圆形标记添加成功", center);
      }

      // 外部调用的函数，用于自适应视图
      function fitView() {
        if (!mapLoaded) {
          console.error("地图尚未初始化，无法调用 fitView");
          return;
        }
        console.log("自适应视图");
        // 计算所有 markers 的中心点
        if (markers.length === 0) {
          console.warn("没有可用的标记，无法自适应视图");
          return;
        }
        var lngSum = 0;
        var latSum = 0;
        markers.forEach((marker) => {
          var center = marker.getCenter();
          lngSum += center.lng;
          latSum += center.lat;
        });
        var centerLng = lngSum / markers.length;
        var centerLat = latSum / markers.length;

        // 设置地图中心点和缩放级别
        map.setZoomAndCenter(5.5, [centerLng, centerLat], true, 500);
      }
    </script>
  </body>
</html>
