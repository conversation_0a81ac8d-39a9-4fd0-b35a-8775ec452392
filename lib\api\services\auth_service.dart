import 'package:dio/dio.dart';
import '../network/http_client.dart';
import '../../models/user_model.dart';
import '../../models/api_response.dart';

/// 认证服务类
/// 
/// 处理用户登录、注册和更新相关的API请求
class AuthService {
  final HttpClient _httpClient;

  AuthService(this._httpClient);

  /// 用户登录
  /// 
  /// [request] 登录请求参数
  /// 返回包含token和用户信息的响应
  Future<ApiResponse<AuthResponse>> login(LoginRequest request) async {
    try {
      final response = await _httpClient.post('/user/login', data: request.toJson());
      
      return ApiResponse.fromJson(
        response.data as Map<String, dynamic>,
        (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// 用户注册
  /// 
  /// [request] 注册请求参数
  /// 返回包含token和用户信息的响应
  Future<ApiResponse<AuthResponse>> register(RegisterRequest request) async {
    try {
      final response = await _httpClient.post('/user/register', data: request.toJson());
      
      return ApiResponse.fromJson(
        response.data as Map<String, dynamic>,
        (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// 更新用户信息
  /// 
  /// [request] 用户更新请求参数
  /// 返回包含更新后token和用户信息的响应
  Future<ApiResponse<AuthResponse>> updateUser(UserUpdateRequest request) async {
    try {
      final response = await _httpClient.post('/user/update', data: request.toJson());
      
      return ApiResponse.fromJson(
        response.data as Map<String, dynamic>,
        (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// 处理Dio异常
  Exception _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception('网络连接超时，请检查网络设置');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.message ?? '请求失败';
        if (statusCode == 401) {
          return Exception('认证失败，请重新登录');
        } else if (statusCode == 403) {
          return Exception('权限不足');
        } else if (statusCode == 404) {
          return Exception('请求的资源不存在');
        } else if (statusCode == 500) {
          return Exception('服务器内部错误');
        }
        return Exception('请求失败: $message');
      case DioExceptionType.cancel:
        return Exception('请求已取消');
      case DioExceptionType.connectionError:
        return Exception('网络连接错误，请检查网络设置');
      default:
        return Exception('未知错误: ${e.message}');
    }
  }
}
