import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../api/network/http_client.dart';
import '../api/services/souvenir_service.dart';
import '../models/souvenir/souvenir_item.dart';
import '../models/pagination_model.dart';

/// 奖励服务Provider
final souvenirServiceProvider = Provider<SouvenirService>((ref) {
  final httpClient = ref.watch(httpClientProvider);
  return SouvenirService(httpClient);
});

/// 奖励列表查询参数
class SouvenirQuery {
  final String? category;
  final int page;
  final int limit;

  const SouvenirQuery({this.category, this.page = 1, this.limit = 20});

  SouvenirQuery copyWith({String? category, int? page, int? limit}) {
    return SouvenirQuery(
      category: category ?? this.category,
      page: page ?? this.page,
      limit: limit ?? this.limit,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SouvenirQuery &&
        other.category == category &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode => Object.hash(category, page, limit);

  @override
  String toString() {
    return 'SouvenirQuery(category: $category, page: $page, limit: $limit)';
  }
}

/// 奖励列表状态
class SouvenirListState {
  final List<SouvenirItem> items;
  final PaginationModel? pagination;
  final bool isLoading;
  final String? error;
  final bool hasMore;

  const SouvenirListState({
    this.items = const [],
    this.pagination,
    this.isLoading = false,
    this.error,
    this.hasMore = true,
  });

  SouvenirListState copyWith({
    List<SouvenirItem>? items,
    PaginationModel? pagination,
    bool? isLoading,
    String? error,
    bool? hasMore,
  }) {
    return SouvenirListState(
      items: items ?? this.items,
      pagination: pagination ?? this.pagination,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasMore: hasMore ?? this.hasMore,
    );
  }

  @override
  String toString() {
    return 'SouvenirListState(items: ${items.length}, isLoading: $isLoading, hasMore: $hasMore, error: $error)';
  }
}

/// 奖励列表Provider
final souvenirListProvider =
    StateNotifierProvider.family<SouvenirListNotifier, SouvenirListState, SouvenirQuery>((
      ref,
      query,
    ) {
      final souvenirService = ref.watch(souvenirServiceProvider);
      return SouvenirListNotifier(souvenirService, query);
    });

/// 奖励列表状态管理器
class SouvenirListNotifier extends StateNotifier<SouvenirListState> {
  final SouvenirService _souvenirService;
  final SouvenirQuery _initialQuery;

  SouvenirListNotifier(this._souvenirService, this._initialQuery)
    : super(const SouvenirListState()) {
    loadSouvenirs();
  }

  /// 加载奖励列表
  Future<void> loadSouvenirs({bool refresh = false}) async {
    if (state.isLoading) return;

    final query = refresh
        ? _initialQuery
        : _initialQuery.copyWith(page: state.pagination?.currentPage ?? 1);

    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _souvenirService.getSouvenirs(
        category: query.category,
        page: query.page,
        limit: query.limit,
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final newItems = refresh ? data.items : [...state.items, ...data.items];

        state = state.copyWith(
          items: newItems,
          pagination: data.pagination,
          isLoading: false,
          hasMore: data.pagination.hasNextPage,
        );
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 加载更多奖励
  Future<void> loadMore() async {
    if (!state.hasMore || state.isLoading) return;

    final nextPage = (state.pagination?.currentPage ?? 0) + 1;
    final query = _initialQuery.copyWith(page: nextPage);

    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _souvenirService.getSouvenirs(
        category: query.category,
        page: query.page,
        limit: query.limit,
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final newItems = [...state.items, ...data.items];

        state = state.copyWith(
          items: newItems,
          pagination: data.pagination,
          isLoading: false,
          hasMore: data.pagination.hasNextPage,
        );
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 刷新列表
  Future<void> refresh() async {
    await loadSouvenirs(refresh: true);
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// 默认奖励列表Provider（无分类）
final defaultSouvenirListProvider = souvenirListProvider(const SouvenirQuery());

/// 按分类获取奖励列表的便捷Provider
StateNotifierProvider<SouvenirListNotifier, SouvenirListState>
souvenirListByCategoryProvider(String? category) {
  return souvenirListProvider(SouvenirQuery(category: category));
}

/// 监听分类变化并自动加载数据的Provider
/// 需要配合 selectedCategoryProvider 使用
final souvenirListWithCategoryProvider = Provider<SouvenirListState>((ref) {
  // 这里需要导入 selectedCategoryProvider，但为了避免循环依赖
  // 我们在 widget 中手动处理分类变化
  return ref.watch(defaultSouvenirListProvider);
});
