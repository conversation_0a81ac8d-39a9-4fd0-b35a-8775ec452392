import 'package:flutter_riverpod/flutter_riverpod.dart';

class AppState {
  bool isBottomNavigationBarVisible = true;

  AppState({this.isBottomNavigationBarVisible = true});

  AppState copyWith({bool? showBottomNavigationBar}) {
    return AppState(
      isBottomNavigationBarVisible:
          showBottomNavigationBar ?? isBottomNavigationBarVisible,
    );
  }

  @override
  String toString() {
    return 'AppState(showBottomNavigationBar: $isBottomNavigationBarVisible)';
  }
}

final appProvider = AsyncNotifierProvider.autoDispose<AppProvider, AppState>(
  () => AppProvider(),
);

class AppProvider extends AutoDisposeAsyncNotifier<AppState> {
  @override
  AppState build() {
    return AppState();
  }

  void hideBottomNavigationBar() {
    state = AsyncValue.data(AppState()..isBottomNavigationBarVisible = false);
  }

  void showBottomNavigationBar() {
    state = AsyncValue.data(AppState()..isBottomNavigationBarVisible = true);
  }

  void toggleBottomNavigationBarVisibility() {
    final currentState = state.valueOrNull ?? AppState();
    final newState = currentState.copyWith(
      showBottomNavigationBar: !currentState.isBottomNavigationBarVisible,
    );
    state = AsyncValue.data(newState);
  }
}
