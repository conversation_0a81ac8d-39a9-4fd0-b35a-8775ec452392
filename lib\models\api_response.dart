import 'pagination_model.dart';

/// 通用API响应模型
///
/// 包含code、message和data字段
class ApiResponse<T> {
  final int code;
  final String message;
  final T? data;

  const ApiResponse({required this.code, required this.message, this.data});

  /// 从JSON创建响应模型
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      code: json['code'] as int,
      message: json['message'] as String,
      data: json['data'] != null && fromJsonT != null
          ? fromJsonT(json['data'])
          : json['data'] as T?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson([Map<String, dynamic> Function(T)? toJsonT]) {
    return {
      'code': code,
      'message': message,
      if (data != null) 'data': toJsonT != null ? toJsonT(data as T) : data,
    };
  }

  /// 判断是否成功
  bool get isSuccess => code == 200;

  /// 判断是否失败
  bool get isFailure => !isSuccess;

  @override
  String toString() {
    return 'ApiResponse(code: $code, message: $message, data: $data)';
  }
}

/// 分页数据响应模型
class PaginatedResponse<T> {
  final List<T> items;
  final PaginationModel pagination;

  const PaginatedResponse({required this.items, required this.pagination});

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    final itemsJson = json['items'] as List<dynamic>;
    final items = itemsJson
        .map((item) => fromJsonT(item as Map<String, dynamic>))
        .toList();

    return PaginatedResponse<T>(
      items: items,
      pagination: PaginationModel.fromJson(json['pagination'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {'items': items.map(toJsonT).toList(), 'pagination': pagination.toJson()};
  }
}
