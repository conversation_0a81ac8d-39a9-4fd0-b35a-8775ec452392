/// 列表奖励项模型类
///
/// 用于列表中的奖励项
/// 包含ID、标题、图片URL、类别和创建时间
class SouvenirItem {
  // 奖励ID
  final int souvenirId;
  // 标题
  final String title;
  // 图片URL
  final String imgUrl;
  // 奖励类别
  final String category;
  // 创建时间
  final String createdAt;

  const SouvenirItem({
    required this.souvenirId,
    required this.title,
    required this.imgUrl,
    required this.category,
    required this.createdAt,
  });

  // 从JSON创建对象
  factory SouvenirItem.fromJson(Map<String, dynamic> json) {
    return SouvenirItem(
      souvenirId: json['souvenir_id'] as int,
      title: json['title'] as String,
      imgUrl: json['img_url'] as String,
      category: json['category'] as String,
      createdAt: json['created_at'] as String,
    );
  }

  // 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'souvenir_id': souvenirId,
      'title': title,
      'img_url': imgUrl,
      'category': category,
      'created_at': createdAt,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SouvenirItem && other.souvenirId == souvenirId;
  }

  @override
  int get hashCode => souvenirId.hashCode;
}
