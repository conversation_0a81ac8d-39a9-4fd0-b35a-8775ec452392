import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:chickafocus/pages/profile_page.dart';
import 'package:chickafocus/providers/auth_provider.dart';
import 'package:chickafocus/models/user_model.dart';

// 创建一个测试用的 AuthNotifier
class TestAuthNotifier extends AuthNotifier {
  TestAuthNotifier() : super(null as dynamic, null as dynamic) {
    state = AuthState(
      user: UserModel(
        id: 1,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        uid: 'test_uid',
        nickname: '测试用户',
        avatar: '',
      ),
      token: 'test_token',
    );
  }
}

void main() {
  group('ProfilePage 下拉菜单测试', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer(
        overrides: [authProvider.overrideWith((ref) => TestAuthNotifier())],
      );
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('下拉菜单应该显示所有时间范围选项', (WidgetTester tester) async {
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(home: ProfilePage()),
        ),
      );

      // 查找下拉菜单
      final dropdownFinder = find.byType(DropdownMenu<String>);
      expect(dropdownFinder, findsOneWidget);

      // 点击下拉菜单
      await tester.tap(dropdownFinder);
      await tester.pumpAndSettle();

      // 验证所有选项都存在
      expect(find.text('最近7天'), findsWidgets);
      expect(find.text('最近14天'), findsOneWidget);
      expect(find.text('最近30天'), findsOneWidget);
      expect(find.text('最近60天'), findsOneWidget);
    });

    testWidgets('选择不同的时间范围应该更新状态', (WidgetTester tester) async {
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(home: ProfilePage()),
        ),
      );

      // 查找下拉菜单
      final dropdownFinder = find.byType(DropdownMenu<String>);

      // 点击下拉菜单
      await tester.tap(dropdownFinder);
      await tester.pumpAndSettle();

      // 选择"最近14天"选项
      await tester.tap(find.text('最近14天').last);
      await tester.pumpAndSettle();

      // 验证选择已更新（这里可以通过检查图表数据点数量来验证）
      // 由于图表组件的复杂性，这里主要验证没有错误发生
      expect(tester.takeException(), isNull);
    });

    testWidgets('下拉菜单应该有正确的样式', (WidgetTester tester) async {
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(home: ProfilePage()),
        ),
      );

      // 查找动画容器
      final animatedContainerFinder = find.byType(AnimatedContainer);
      expect(animatedContainerFinder, findsWidgets);

      // 查找下拉菜单
      final dropdownFinder = find.byType(DropdownMenu<String>);
      expect(dropdownFinder, findsOneWidget);

      // 验证下拉菜单的基本属性
      final dropdown = tester.widget<DropdownMenu<String>>(dropdownFinder);
      expect(dropdown.width, equals(140));
      expect(dropdown.initialSelection, equals('最近7天'));
    });
  });
}
