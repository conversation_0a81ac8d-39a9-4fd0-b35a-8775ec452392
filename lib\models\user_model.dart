/// 用户信息数据模型
class UserModel {
  final int id;
  final String createdAt;
  final String updatedAt;
  final String uid;
  final String nickname;
  final String avatar;

  const UserModel({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.uid,
    required this.nickname,
    required this.avatar,
  });

  /// 从JSON创建用户模型
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as int,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
      uid: json['uid'] as String,
      nickname: json['nickname'] as String,
      avatar: json['avatar'] as String,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'uid': uid,
      'nickname': nickname,
      'avatar': avatar,
    };
  }

  /// 创建副本
  UserModel copyWith({
    int? id,
    String? createdAt,
    String? updatedAt,
    String? uid,
    String? nickname,
    String? avatar,
  }) {
    return UserModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uid: uid ?? this.uid,
      nickname: nickname ?? this.nickname,
      avatar: avatar ?? this.avatar,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserModel(id: $id, uid: $uid, nickname: $nickname)';
  }
}

/// 登录请求模型
class LoginRequest {
  final String credentialType;
  final String credentialIdentifier;
  final String? credentialSecret;

  const LoginRequest({
    required this.credentialType,
    required this.credentialIdentifier,
    this.credentialSecret,
  });

  Map<String, dynamic> toJson() {
    return {
      'credential_type': credentialType,
      'credential_identifier': credentialIdentifier,
      if (credentialSecret != null) 'credential_secret': credentialSecret,
    };
  }
}

/// 注册请求模型
class RegisterRequest {
  final String nickname;
  final String? avatar;
  final String credentialType;
  final String credentialIdentifier;
  final String? credentialSecret;

  const RegisterRequest({
    required this.nickname,
    this.avatar,
    required this.credentialType,
    required this.credentialIdentifier,
    this.credentialSecret,
  });

  Map<String, dynamic> toJson() {
    return {
      'nickname': nickname,
      if (avatar != null) 'avatar': avatar,
      'credential_type': credentialType,
      'credential_identifier': credentialIdentifier,
      if (credentialSecret != null) 'credential_secret': credentialSecret,
    };
  }
}

/// 用户更新请求模型
class UserUpdateRequest {
  final String nickname;
  final String avatar;

  const UserUpdateRequest({required this.nickname, required this.avatar});

  Map<String, dynamic> toJson() {
    return {'nickname': nickname, 'avatar': avatar};
  }
}

/// 认证响应模型
class AuthResponse {
  final String token;
  final UserModel user;

  const AuthResponse({required this.token, required this.user});

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      token: json['token'] as String,
      user: UserModel.fromJson(json['user'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {'token': token, 'user': user.toJson()};
  }
}
