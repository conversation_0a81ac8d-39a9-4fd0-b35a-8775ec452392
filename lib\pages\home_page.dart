import 'package:chickafocus/providers/app_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

enum WorkingState { ready, working }

/// 主屏幕页面
///
/// 包含地图、计时选择两个主要的页面，
/// 由于内部存在重复元素和动画逻辑，
/// 所以将其抽象为一个单独的页面组件。
class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  /// 是否加载了地图
  bool _isMapLoaded = false;
  // 驱动动画的状态
  WorkingState _currentState = WorkingState.ready;
  final _animationDurationNumber = 700;
  late final _animationDuration = Duration(
    milliseconds: _animationDurationNumber,
  );
  // Webview 控制器
  InAppWebViewController? webViewController;

  /// 北京经纬度
  final List<double> _beijingPosition = [116.4074, 39.9042];

  /// 上海经纬度
  final List<double> _shanghaiPosition = [121.4737, 31.2304];

  final InAppLocalhostServer localhostServer = InAppLocalhostServer(
    documentRoot: 'assets/html/map',
  );

  /// 切换状态
  void _toggleState() {
    setState(() {
      _currentState = _currentState == WorkingState.ready
          ? WorkingState.working
          : WorkingState.ready;
    });
  }

  (double, double, double) getPostionValues() {
    // 根据屏幕方向返回定位值
    // 竖屏状态下定位到底部中心
    // 横屏状态下定位到右下角
    if (MediaQuery.of(context).orientation == Orientation.portrait) {
      return (16, 32, 16);
    } else {
      final screenSize = MediaQuery.of(context).size;
      return (screenSize.height - 32, 0, 16);
    }
  }

  Future<void> _mapSetZoomAndCenter() async {
    if (webViewController != null) {
      await webViewController!.evaluateJavascript(
        source:
            'setZoomAndCenter(7, [${_beijingPosition[0]}, ${_beijingPosition[1]}], false, $_animationDurationNumber);',
      );
    }
  }

  Future<void> _addCircleMarker(double lng, double lat, double radius) async {
    if (webViewController != null) {
      final source = 'addCircleMarker($lng, $lat, $radius);';
      debugPrint('Adding circle marker with source: $source');
      await webViewController!.evaluateJavascript(source: source);
    }
  }

  Future<void> _fitView() async {
    if (webViewController != null) {
      await webViewController!.evaluateJavascript(source: 'fitView();');
    }
  }

  @override
  void initState() {
    super.initState();
    // 启动本地服务器
    localhostServer.start();
  }

  @override
  void dispose() {
    // 停止本地服务器
    localhostServer.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final (pl, pb, pr) = getPostionValues();
    final screen = MediaQuery.of(context).size;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    const titleTextSize = 32.0;
    const textStyle = TextStyle(
      fontSize: titleTextSize,
      color: Colors.black,
      fontWeight: FontWeight.bold,
    );
    final mainBtnWidth = 172.0;

    return Material(
      child: Stack(
        children: [
          AnimatedOpacity(
            opacity: _isMapLoaded ? 1.0 : 0.0,
            curve: Curves.easeInOut,
            duration: _animationDuration,
            child: InAppWebView(
              initialSettings: InAppWebViewSettings(isInspectable: kDebugMode),
              initialUrlRequest: URLRequest(
                url: WebUri("http://localhost:8080/index.html"),
              ),
              onWebViewCreated: (controller) {
                webViewController = controller;
                debugPrint('WebView created');
              },
              onLoadStart: (controller, url) {
                debugPrint('WebView started loading: $url');
              },
              onLoadStop: (controller, url) async {
                debugPrint('WebView stopped loading: $url');
                // 添加两个城市的点
                await Future.wait([
                  _addCircleMarker(
                    _beijingPosition[0],
                    _beijingPosition[1],
                    16.0,
                  ),
                  _addCircleMarker(
                    _shanghaiPosition[0],
                    _shanghaiPosition[1],
                    16.0,
                  ),
                ]);
                // 适应视图
                await _fitView();
                // 刷新 UI
                if (mounted) {
                  setState(() {
                    debugPrint('_isMapLoaded set to true');
                    _isMapLoaded = true;
                  });
                }
              },
              onConsoleMessage: (controller, consoleMessage) {
                debugPrint(
                  'WebView console message: ${consoleMessage.message}',
                );
              },
            ),
          ),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: statusBarHeight + 128,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.white, Colors.white.withValues(alpha: 0)],
                ),
              ),
              padding: EdgeInsets.only(
                top: statusBarHeight + 32,
                left: 32,
                right: 32,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AnimatedOpacity(
                    opacity: _currentState == WorkingState.working ? 0.0 : 1.0,
                    duration: _animationDuration,
                    child: Text(
                      '正在进行旅程',
                      style: textStyle.copyWith(fontSize: titleTextSize * 0.75),
                    ),
                  ),
                  AnimatedOpacity(
                    opacity: _currentState == WorkingState.working ? 0.0 : 1.0,
                    duration: _animationDuration,
                    child: const Text('北京—上海', style: textStyle),
                  ),
                ],
              ),
            ),
          ),

          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Center(
              child: AnimatedOpacity(
                opacity: _currentState == WorkingState.working ? 1.0 : 0.0,
                duration: _animationDuration,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.white.withValues(alpha: 0),
                        Colors.white.withValues(alpha: 0.7),
                        Colors.white,
                      ],
                    ),
                  ),
                  width: double.infinity,
                  height: 128,
                  alignment: Alignment.center,
                  child: Text(
                    '北京—上海',
                    style: textStyle.copyWith(
                      fontSize: 24.0,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ),
          AnimatedPositioned(
            duration: _animationDuration,
            curve: Curves.easeInOut,
            bottom: pb + 96,
            right: _currentState == WorkingState.working
                ? (screen.width - mainBtnWidth) / 2
                : 16,
            child: GestureDetector(
              onTap: () {
                ref
                    .read(appProvider.notifier)
                    .toggleBottomNavigationBarVisibility();
                _mapSetZoomAndCenter();
                _toggleState();
              },
              child: Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(32.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      offset: const Offset(0, 2),
                      blurRadius: 4.0,
                    ),
                  ],
                ),
                height: 54,
                width: mainBtnWidth,
                child: const Text(
                  '开始旅程',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
